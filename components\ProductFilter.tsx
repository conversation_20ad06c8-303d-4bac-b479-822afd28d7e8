import React, { useState, useEffect, useMemo } from 'react';
import {
    Box,
    Button,
    Checkbox,
    FormGroup,
    Input,
    Select,
    Text,
    Flex,
} from '@bigcommerce/big-design';
import { ArrowBackIcon, ArrowForwardIcon } from '@bigcommerce/big-design-icons';
import { useProductsForFilter } from '../lib/hooks';

export interface ProductFilterItem {
    id: number;
    name: string;
}

interface ProductFilterProps {
    selectedIds: number[];
    onChange: (selectedIds: number[]) => void;
    error?: string;
}

const ProductFilter: React.FC<ProductFilterProps> = ({
    selectedIds,
    onChange,
    error
}) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [searchTerm, setSearchTerm] = useState('');
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

    // Debounce search term
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchTerm(searchTerm);
            setCurrentPage(1); // Reset to first page when searching
        }, 300);

        return () => clearTimeout(timer);
    }, [searchTerm]);

    const { products, meta, isLoading, error: apiError, fallback } = useProductsForFilter(
        currentPage,
        itemsPerPage,
        debouncedSearchTerm
    );

    // Filter products client-side if using fallback data
    const filteredProducts = useMemo(() => {
        if (!fallback || !debouncedSearchTerm) {
            return products;
        }
        
        return products.filter(product => 
            product.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        );
    }, [products, debouncedSearchTerm, fallback]);

    const totalItems = meta?.pagination?.total || filteredProducts.length;
    const totalPages = meta?.pagination?.total_pages || Math.ceil(totalItems / itemsPerPage);
    const startItem = ((currentPage - 1) * itemsPerPage) + 1;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            // Add all visible products to selection (don't remove existing selections from other pages)
            const newSelections = filteredProducts.map(product => product.id);
            const combinedSelections = [...new Set([...selectedIds, ...newSelections])];
            onChange(combinedSelections);
        } else {
            // Remove all visible products from selection
            const visibleProductIds = filteredProducts.map(product => product.id);
            const remainingSelections = selectedIds.filter(id => !visibleProductIds.includes(id));
            onChange(remainingSelections);
        }
    };

    const handleItemChange = (productId: number, checked: boolean) => {
        if (checked) {
            onChange([...selectedIds, productId]);
        } else {
            onChange(selectedIds.filter(id => id !== productId));
        }
    };

    const handleItemsPerPageChange = (value: string) => {
        setItemsPerPage(Number(value));
        setCurrentPage(1);
    };

    const handlePreviousPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    const handleNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const visibleProductIds = filteredProducts.map(product => product.id);
    const isAllVisibleSelected = visibleProductIds.length > 0 && 
        visibleProductIds.every(id => selectedIds.includes(id));
    const isIndeterminate = visibleProductIds.some(id => selectedIds.includes(id)) && 
        !isAllVisibleSelected;

    if (isLoading) {
        return (
            <FormGroup>
                <Text bold>Products</Text>
                <Text color="secondary60" marginBottom="small">
                    Select specific products to apply this fee to. If no products are selected, the fee will apply to all products (subject to other filters).
                </Text>
                <Box padding="medium">
                    <Text color="secondary60">Loading products...</Text>
                </Box>
            </FormGroup>
        );
    }

    return (
        <FormGroup>
            <Text bold>Products</Text>
            <Text color="secondary60" marginBottom="small">
                Select specific products to apply this fee to. If no products are selected, the fee will apply to all products (subject to other filters).
            </Text>
            
            {(error || apiError) && (
                <Text color="danger" marginBottom="small">
                    {error || apiError}
                </Text>
            )}

            {fallback && (
                <Text color="warning" marginBottom="small">
                    Limited access to products data. Using sample data for demonstration.
                </Text>
            )}

            {/* Search Input */}
            <Box marginBottom="medium">
                <Input
                    placeholder="Search products by name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                />
            </Box>

            {/* Pagination Controls */}
            <Flex justifyContent="space-between" alignItems="center" marginBottom="medium">
                <Box>
                    <Select
                        value={String(itemsPerPage)}
                        onOptionChange={handleItemsPerPageChange}
                        options={[
                            { value: '10', content: 'Show 10 items' },
                            { value: '20', content: 'Show 20 items' },
                            { value: '30', content: 'Show 30 items' },
                            { value: '40', content: 'Show 40 items' },
                        ]}
                    />
                </Box>
                
                <Text color="secondary60">
                    Showing {totalItems > 0 ? startItem : 0}-{endItem} of {totalItems} products
                </Text>
                
                <Flex alignItems="center">
                    <Button
                        variant="subtle"
                        iconOnly={<ArrowBackIcon />}
                        onClick={handlePreviousPage}
                        disabled={currentPage <= 1}
                        marginRight="xSmall"
                    />
                    <Text marginX="small">
                        Page {currentPage} of {totalPages || 1}
                    </Text>
                    <Button
                        variant="subtle"
                        iconOnly={<ArrowForwardIcon />}
                        onClick={handleNextPage}
                        disabled={currentPage >= totalPages}
                        marginLeft="xSmall"
                    />
                </Flex>
            </Flex>

            {/* Product List */}
            <Box
                border="box"
                borderRadius="normal"
                padding="medium"
                style={{ maxHeight: '300px', overflowY: 'auto' }}
            >
                {filteredProducts.length === 0 ? (
                    <Text color="secondary60">
                        {debouncedSearchTerm ? 'No products found matching your search.' : 'No products available'}
                    </Text>
                ) : (
                    <>
                        {/* Select All checkbox */}
                        <Box marginBottom="small" paddingBottom="small" borderBottom="box">
                            <Checkbox
                                label="Select All (visible)"
                                checked={isAllVisibleSelected}
                                isIndeterminate={isIndeterminate}
                                onChange={(e) => handleSelectAll(e.target.checked)}
                            />
                        </Box>

                        {/* Individual product checkboxes */}
                        {filteredProducts.map((product) => (
                            <Box key={product.id} marginBottom="xSmall">
                                <Checkbox
                                    label={product.name}
                                    checked={selectedIds.includes(product.id)}
                                    onChange={(e) => handleItemChange(product.id, e.target.checked)}
                                />
                            </Box>
                        ))}
                    </>
                )}
            </Box>
            
            {selectedIds.length > 0 && (
                <Text color="secondary60" marginTop="xSmall">
                    {selectedIds.length} product{selectedIds.length !== 1 ? 's' : ''} selected
                </Text>
            )}
        </FormGroup>
    );
};

export default ProductFilter;
