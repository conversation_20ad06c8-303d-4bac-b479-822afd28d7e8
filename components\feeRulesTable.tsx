import {
    Badge,
    <PERSON>,
    Button,
    Dropdown,
    Flex,
    Modal,
    Table,
    Text,
    H3,
} from '@bigcommerce/big-design';
import { MoreHorizIcon } from '@bigcommerce/big-design-icons';
import { useRouter } from 'next/router';
import { ReactElement, useState } from 'react';
import { useSession } from '../context/session';
import { FeeRuleData } from '../types/db';

interface FeeRulesTableProps {
    feeRules: FeeRuleData[];
    isLoading: boolean;
    onRefresh: () => void;
}

const FeeRulesTable = ({ feeRules, isLoading, onRefresh }: FeeRulesTableProps) => {
    const router = useRouter();
    const { context } = useSession();
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);
    const [selectedFeeRule, setSelectedFeeRule] = useState<FeeRuleData | null>(null);
    const [isDeleting, setIsDeleting] = useState(false);

    const formatCost = (cost: number, type: 'percentage' | 'fixed'): string => {
        if (type === 'percentage') {
            return `${cost}%`;
        }
        return `$${cost.toFixed(2)}`;
    };

    const formatDate = (timestamp: number): string => {
        return new Date(timestamp).toLocaleDateString();
    };

    const renderName = (name: string): ReactElement => (
        <Text bold>{name}</Text>
    );

    const renderType = (type: 'percentage' | 'fixed'): ReactElement => (
        <Badge 
            variant={type === 'percentage' ? 'secondary' : 'primary'} 
            label={type === 'percentage' ? 'Percentage' : 'Fixed Amount'}
        />
    );

    const renderCost = (cost: number, type: 'percentage' | 'fixed'): ReactElement => (
        <Text bold>{formatCost(cost, type)}</Text>
    );

    const renderActive = (active: boolean): ReactElement => (
        <Badge 
            variant={active ? 'success' : 'secondary'} 
            label={active ? 'Active' : 'Inactive'}
        />
    );

    const renderOptionalField = (value?: string | number): ReactElement => (
        <Text color={value ? 'primary' : 'secondary60'}>
            {value || '—'}
        </Text>
    );

    const handleEdit = (feeRule: FeeRuleData) => {
        router.push(`/edit-fee/${feeRule.id}`);
    };

    const handleDeleteClick = (feeRule: FeeRuleData) => {
        setSelectedFeeRule(feeRule);
        setDeleteModalOpen(true);
    };

    const handleDeleteConfirm = async () => {
        if (!selectedFeeRule) return;

        setIsDeleting(true);
        try {
            const response = await fetch(`/api/fee-rules?context=${context}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ feeRuleId: selectedFeeRule.id }),
            });

            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || 'Failed to delete fee rule');
            }

            // Show success message with synchronization results
            let successMessage = 'Fee rule deleted successfully!';

            if (result.synchronization) {
                if (result.synchronization.attempted) {
                    if (result.synchronization.success) {
                        successMessage += ` BigCommerce fee removed from checkout ${result.synchronization.checkoutId}.`;
                    } else {
                        successMessage += ` Note: BigCommerce synchronization failed (${result.synchronization.error}).`;
                    }
                } else {
                    successMessage += ` ${result.synchronization.reason}.`;
                }
            }

            // Log success message and any synchronization details
            console.log(successMessage);
            if (result.synchronization?.details) {
                console.log('Synchronization details:', result.synchronization.details);
            }

            // Refresh the table data
            onRefresh();
            setDeleteModalOpen(false);
            setSelectedFeeRule(null);
        } catch (error) {
            console.error('Error deleting fee rule:', error);
            // You could add a toast notification here
        } finally {
            setIsDeleting(false);
        }
    };

    const handleDeleteCancel = () => {
        setDeleteModalOpen(false);
        setSelectedFeeRule(null);
    };

    const renderAction = (feeRule: FeeRuleData): ReactElement => (
        <Dropdown
            items={[
                {
                    content: 'Edit',
                    onItemClick: () => handleEdit(feeRule),
                    hash: 'edit'
                },
                {
                    content: 'Delete',
                    onItemClick: () => handleDeleteClick(feeRule),
                    hash: 'delete'
                }
            ]}
            toggle={<Button iconOnly={<MoreHorizIcon color="secondary60" />} variant="subtle" />}
        />
    );

    if (feeRules.length === 0 && !isLoading) {
        return (
            <Box marginTop="medium">
                <Flex
                    backgroundColor="secondary10"
                    borderRadius="normal"
                    padding="large"
                    justifyContent="center"
                    flexDirection="column"
                    alignItems="center"
                >
                    <Text>No fee rules found for this store.</Text>
                    <Text color="secondary60" marginTop="xSmall">
                        Create your first fee rule to get started.
                    </Text>
                </Flex>
            </Box>
        );
    }

    return (
        <>
            <Box marginTop="medium">
                <Table
                    columns={[
                        { 
                            header: 'Name', 
                            hash: 'name', 
                            render: ({ name }) => renderName(name),
                            width: '15%'
                        },
                        { 
                            header: 'Type', 
                            hash: 'type', 
                            render: ({ type }) => renderType(type),
                            width: '12%'
                        },
                        { 
                            header: 'Display Name', 
                            hash: 'display_name', 
                            render: ({ display_name }) => <Text>{display_name}</Text>,
                            width: '18%'
                        },
                        { 
                            header: 'Cost', 
                            hash: 'cost', 
                            render: ({ cost, type }) => renderCost(cost, type),
                            width: '10%'
                        },
                        { 
                            header: 'Source', 
                            hash: 'source', 
                            render: ({ source }) => renderOptionalField(source),
                            width: '12%'
                        },
                        { 
                            header: 'Tax Class ID', 
                            hash: 'tax_class_id', 
                            render: ({ tax_class_id }) => renderOptionalField(tax_class_id),
                            width: '10%'
                        },
                        { 
                            header: 'Status', 
                            hash: 'active', 
                            render: ({ active }) => renderActive(active),
                            width: '10%'
                        },
                        { 
                            header: 'Created', 
                            hash: 'created_at', 
                            render: ({ created_at }) => <Text>{formatDate(created_at)}</Text>,
                            width: '10%'
                        },
                        { 
                            header: 'Actions', 
                            hideHeader: true, 
                            hash: 'id', 
                            render: (feeRule) => renderAction(feeRule),
                            width: '3%'
                        },
                    ]}
                    items={feeRules}
                    itemName="Fee Rules"
                />
            </Box>

            <Modal
                isOpen={deleteModalOpen}
                onClose={handleDeleteCancel}
                closeOnClickOutside={false}
                closeOnEscKey={!isDeleting}
            >
                <Box padding="medium">
                    <H3 marginBottom="medium">Delete Fee Rule</H3>
                    <Text marginBottom="medium">
                        Are you sure you want to delete the fee rule "{selectedFeeRule?.name}"? 
                        This action cannot be undone.
                    </Text>
                    <Flex justifyContent="flex-end">
                        <Button
                            variant="subtle"
                            marginRight="medium"
                            onClick={handleDeleteCancel}
                            disabled={isDeleting}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            onClick={handleDeleteConfirm}
                            disabled={isDeleting}
                        >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                        </Button>
                    </Flex>
                </Box>
            </Modal>
        </>
    );
};

export default FeeRulesTable;
