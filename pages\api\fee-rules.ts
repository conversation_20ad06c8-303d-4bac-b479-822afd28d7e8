import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from '../../lib/auth';
import db from '../../lib/db';
import { FeeRuleData } from '../../types/db';
import { applyFeesToCheckout, FeeApplicationResult } from '../../lib/fee-application';
import { synchronizeFeeUpdate, synchronizeFeeDeletion, synchronizeFeeActivation } from '../../lib/fee-synchronization';
import { FeeResponse } from '../../types/bigcommerce';

export default async function feeRules(req: NextApiRequest, res: NextApiResponse) {
    if (req.method === 'POST') {
        return handleCreateFeeRule(req, res);
    } else if (req.method === 'GET') {
        return handleGetFeeRules(req, res);
    } else if (req.method === 'PUT') {
        return handleUpdateFeeRule(req, res);
    } else if (req.method === 'DELETE') {
        return handleDeleteFeeRule(req, res);
    } else {
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        res.status(405).json({ error: `Method ${req.method} Not Allowed` });
        return;
    }
}

async function handleCreateFeeRule(req: NextApiRequest, res: NextApiResponse) {
    try {
        // Get the session to extract store information
        const { storeHash } = await getSession(req);

        const { name, type, display_name, cost, source, tax_class_id, active, selectedBrands, selectedCategories, selectedCustomerGroups, selectedChannels, selectedProducts } = req.body;

        // Validate required fields
        if (!name || !type || !display_name || cost === undefined || !source || active === undefined) {
            res.status(400).json({
                error: 'Missing required fields: name, type, display_name, cost, source, and active are required'
            });
            return;
        }

        // Validate source field
        if (!source.trim()) {
            res.status(400).json({
                error: 'Source field cannot be empty - required by BigCommerce API'
            });
            return;
        }

        // Validate type
        if (!['percentage', 'fixed'].includes(type)) {
            res.status(400).json({
                error: 'Type must be either "percentage" or "fixed"'
            });
            return;
        }

        // Validate cost
        if (typeof cost !== 'number' || cost < 0) {
            res.status(400).json({
                error: 'Cost must be a non-negative number'
            });
            return;
        }

        // Validate active
        if (typeof active !== 'boolean') {
            res.status(400).json({
                error: 'Active must be a boolean'
            });
            return;
        }

        const feeRuleData: FeeRuleData = {
            name: name.trim(),
            type,
            display_name: display_name.trim(),
            cost,
            source: source.trim(), // Now required field
            active,
            created_at: Date.now(),
            storeHash,
        };

        // Only add optional fields if they have valid values
        if (tax_class_id !== undefined && tax_class_id !== null && typeof tax_class_id === 'number') {
            feeRuleData.tax_class_id = tax_class_id;
        }

        // Add filter fields if they exist (including empty arrays)
        if (selectedBrands !== undefined && Array.isArray(selectedBrands)) {
            feeRuleData.selectedBrands = selectedBrands;
        }

        if (selectedCategories !== undefined && Array.isArray(selectedCategories)) {
            feeRuleData.selectedCategories = selectedCategories;
        }

        if (selectedCustomerGroups !== undefined && Array.isArray(selectedCustomerGroups)) {
            feeRuleData.selectedCustomerGroups = selectedCustomerGroups;
        }

        // Add channels filter field
        if (selectedChannels !== undefined && Array.isArray(selectedChannels)) {
            feeRuleData.selectedChannels = selectedChannels;
        }

        // Add products filter field
        if (selectedProducts !== undefined && Array.isArray(selectedProducts)) {
            feeRuleData.selectedProducts = selectedProducts;
        }

        // Store the fee rule in the database
        const feeRuleId = await db.storeFeeRule(feeRuleData);

        // Prepare the response data
        const responseData = {
            id: feeRuleId,
            ...feeRuleData
        };

        // Automatic fee application to most recent checkout (if available)
        let feeApplicationResult: FeeApplicationResult | null = null;
        let recentCheckouts = [];
        try {
            // Only attempt fee application if the new fee rule is active
            if (feeRuleData.active) {
                console.log(`New active fee rule "${feeRuleData.name}" created. Attempting automatic fee application...`);

                // Get the most recent checkout ID for this store
                recentCheckouts = await db.getRecentCheckoutIds(storeHash, 1);

                if (recentCheckouts.length > 0) {
                    const mostRecentCheckout = recentCheckouts[0];
                    console.log(`Applying fees to most recent checkout: ${mostRecentCheckout.checkoutId}`);

                    // Apply all active fee rules (including the newly created one) to the checkout
                    feeApplicationResult = await applyFeesToCheckout(storeHash, mostRecentCheckout.checkoutId);

                    console.log(`Fee application completed for checkout ${mostRecentCheckout.checkoutId}:`, {
                        success: feeApplicationResult.success,
                        appliedFeesCount: feeApplicationResult.appliedFees.length,
                        errorsCount: feeApplicationResult.errors.length
                    });
                } else {
                    console.log('No recent checkouts found for automatic fee application');
                }
            } else {
                console.log(`Fee rule "${feeRuleData.name}" is inactive. Skipping automatic fee application.`);
            }
        } catch (error) {
            console.error('Error during automatic fee application:', error);
            // Don't fail the fee rule creation if fee application fails
            feeApplicationResult = {
                success: false,
                appliedFees: [],
                errors: [{ feeRule: null, error: error.message || 'Unknown error occurred' }]
            };
        }

        res.status(201).json({
            success: true,
            message: 'Fee rule created successfully',
            data: responseData,
            feeApplication: feeApplicationResult ? {
                attempted: true,
                success: feeApplicationResult.success,
                appliedFeesCount: feeApplicationResult.appliedFees.length,
                errorsCount: feeApplicationResult.errors.length,
                checkoutId: feeApplicationResult.success && recentCheckouts?.length > 0 ? recentCheckouts[0].checkoutId : null,
                details: {
                    appliedFees: feeApplicationResult.appliedFees.map((fee: FeeResponse) => ({
                        id: fee.id,
                        name: fee.name,
                        cost: fee.cost || fee.cost_inc_tax || fee.cost_ex_tax || 0
                    })),
                    errors: feeApplicationResult.errors.map((error: any) => ({
                        feeRuleName: error.feeRule?.name || 'Unknown',
                        error: error.error
                    })),
                    trackingUpdates: feeApplicationResult.trackingUpdates?.map(update => ({
                        feeRuleId: update.feeRuleId,
                        feeRuleName: update.feeRuleName,
                        bigCommerceFeeId: update.bigCommerceFeeId,
                        success: update.success,
                        error: update.error
                    })) || []
                }
            } : {
                attempted: false,
                reason: feeRuleData.active ? 'No recent checkouts available' : 'Fee rule is inactive'
            }
        });

    } catch (error) {
        console.error('Error creating fee rule:', error);
        res.status(500).json({ 
            error: 'Internal server error',
            message: error.message 
        });
    }
}

async function handleGetFeeRules(req: NextApiRequest, res: NextApiResponse) {
    try {
        // Get the session to extract store information
        const { storeHash } = await getSession(req);

        // Retrieve fee rules from the database
        const feeRules = await db.getFeeRules(storeHash);

        res.status(200).json({
            success: true,
            data: feeRules,
            count: feeRules.length,
            storeHash,
        });

    } catch (error) {
        console.error('Error retrieving fee rules:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
}

async function handleUpdateFeeRule(req: NextApiRequest, res: NextApiResponse) {
    try {
        // Get the session to extract store information
        const { storeHash } = await getSession(req);

        const { feeRuleId, name, type, display_name, cost, source, tax_class_id, active, selectedBrands, selectedCategories, selectedCustomerGroups, selectedChannels, selectedProducts } = req.body;

        // Validate required fields
        if (!feeRuleId) {
            res.status(400).json({
                error: 'Missing required field: feeRuleId is required'
            });
            return;
        }

        if (!name || !type || !display_name || cost === undefined || !source || active === undefined) {
            res.status(400).json({
                error: 'Missing required fields: name, type, display_name, cost, source, and active are required'
            });
            return;
        }

        // Validate source field
        if (!source.trim()) {
            res.status(400).json({
                error: 'Source field cannot be empty - required by BigCommerce API'
            });
            return;
        }

        // Validate type
        if (!['percentage', 'fixed'].includes(type)) {
            res.status(400).json({
                error: 'Invalid type: must be either "percentage" or "fixed"'
            });
            return;
        }

        // Validate cost
        if (typeof cost !== 'number' || cost < 0) {
            res.status(400).json({
                error: 'Invalid cost: must be a non-negative number'
            });
            return;
        }

        // Validate tax_class_id if provided
        if (tax_class_id !== undefined && (typeof tax_class_id !== 'number' || tax_class_id < 0)) {
            res.status(400).json({
                error: 'Invalid tax_class_id: must be a non-negative number'
            });
            return;
        }

        // Validate active
        if (typeof active !== 'boolean') {
            res.status(400).json({
                error: 'Invalid active: must be a boolean'
            });
            return;
        }

        const updateData: Partial<FeeRuleData> = {
            name: name.trim(),
            type,
            display_name: display_name.trim(),
            cost,
            source: source.trim(), // Now required field
            active,
        };

        // Only add optional fields if they have valid values
        if (tax_class_id !== undefined && tax_class_id !== null && typeof tax_class_id === 'number') {
            updateData.tax_class_id = tax_class_id;
        }

        // Add filter fields if they exist (including empty arrays)
        if (selectedBrands !== undefined && Array.isArray(selectedBrands)) {
            updateData.selectedBrands = selectedBrands;
        }

        if (selectedCategories !== undefined && Array.isArray(selectedCategories)) {
            updateData.selectedCategories = selectedCategories;
        }

        if (selectedCustomerGroups !== undefined && Array.isArray(selectedCustomerGroups)) {
            updateData.selectedCustomerGroups = selectedCustomerGroups;
        }

        // Add channels filter field
        if (selectedChannels !== undefined && Array.isArray(selectedChannels)) {
            updateData.selectedChannels = selectedChannels;
        }

        // Add products filter field
        if (selectedProducts !== undefined && Array.isArray(selectedProducts)) {
            updateData.selectedProducts = selectedProducts;
        }

        // Get the original fee rule before updating for synchronization
        const originalFeeRule = await db.getFeeRule(storeHash, feeRuleId);
        if (!originalFeeRule) {
            res.status(404).json({
                error: 'Fee rule not found'
            });
            return;
        }

        // Update the fee rule in the database
        await db.updateFeeRule(storeHash, feeRuleId, updateData);

        // Get the updated fee rule to return
        const updatedFeeRule = await db.getFeeRule(storeHash, feeRuleId);

        // Detect status changes and perform appropriate synchronization
        let synchronizationResult = null;
        const originalActive = originalFeeRule.active;
        const updatedActive = updatedFeeRule?.active;

        if (originalActive !== updatedActive) {
            // Status changed - handle activation or deactivation
            if (!originalActive && updatedActive) {
                // Fee rule activated (inactive → active)
                try {
                    console.log(`Fee rule "${updatedFeeRule.name}" activated - applying to most recent checkout`);
                    synchronizationResult = await synchronizeFeeActivation(storeHash, updatedFeeRule);

                    if (synchronizationResult.success) {
                        console.log(`Fee activation synchronization successful for rule "${updatedFeeRule.name}"`);
                    } else {
                        console.warn(`Fee activation synchronization failed for rule "${updatedFeeRule.name}":`, synchronizationResult.error);
                    }
                } catch (error) {
                    console.error(`Error during fee activation synchronization for rule "${updatedFeeRule.name}":`, error);
                    synchronizationResult = {
                        success: false,
                        action: 'update',
                        error: error.message || 'Unknown activation synchronization error'
                    };
                }
            } else if (originalActive && !updatedActive) {
                // Fee rule deactivated (active → inactive)
                try {
                    console.log(`Fee rule "${updatedFeeRule.name}" deactivated - removing from checkouts`);
                    synchronizationResult = await synchronizeFeeDeletion(storeHash, originalFeeRule);

                    if (synchronizationResult.success) {
                        console.log(`Fee deactivation synchronization successful for rule "${updatedFeeRule.name}"`);
                    } else {
                        console.warn(`Fee deactivation synchronization failed for rule "${updatedFeeRule.name}":`, synchronizationResult.error);
                    }
                } catch (error) {
                    console.error(`Error during fee deactivation synchronization for rule "${updatedFeeRule.name}":`, error);
                    synchronizationResult = {
                        success: false,
                        action: 'delete',
                        error: error.message || 'Unknown deactivation synchronization error'
                    };
                }
            }
        } else if (updatedActive) {
            // Status unchanged and fee is active - update existing fee
            try {
                console.log(`Attempting fee update synchronization for active rule "${updatedFeeRule.name}"`);
                synchronizationResult = await synchronizeFeeUpdate(storeHash, updatedFeeRule);

                if (synchronizationResult.success) {
                    console.log(`Fee update synchronization successful for rule "${updatedFeeRule.name}"`);
                } else {
                    console.warn(`Fee update synchronization failed for rule "${updatedFeeRule.name}":`, synchronizationResult.error);
                }
            } catch (error) {
                console.error(`Error during fee update synchronization for rule "${updatedFeeRule.name}":`, error);
                synchronizationResult = {
                    success: false,
                    action: 'update',
                    error: error.message || 'Unknown update synchronization error'
                };
            }
        } else {
            console.log(`Skipping fee synchronization for inactive rule "${updatedFeeRule?.name}"`);
        }

        // Determine synchronization reason for response
        let synchronizationReason = 'No synchronization needed';
        if (originalActive !== updatedActive) {
            if (!originalActive && updatedActive) {
                synchronizationReason = 'Fee rule activated - applied to most recent checkout';
            } else if (originalActive && !updatedActive) {
                synchronizationReason = 'Fee rule deactivated - removed from checkouts';
            }
        } else if (updatedActive) {
            synchronizationReason = 'Fee rule updated - synchronized with existing checkouts';
        } else {
            synchronizationReason = 'Fee rule is inactive - no synchronization performed';
        }

        res.status(200).json({
            success: true,
            message: 'Fee rule updated successfully',
            data: updatedFeeRule,
            synchronization: synchronizationResult ? {
                attempted: true,
                success: synchronizationResult.success,
                action: synchronizationResult.action,
                checkoutId: synchronizationResult.checkoutId,
                bigCommerceFeeId: synchronizationResult.bigCommerceFeeId,
                error: synchronizationResult.error,
                details: synchronizationResult.details,
                reason: synchronizationReason
            } : {
                attempted: false,
                reason: synchronizationReason
            }
        });

    } catch (error) {
        console.error('Error updating fee rule:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
}

async function handleDeleteFeeRule(req: NextApiRequest, res: NextApiResponse) {
    try {
        // Get the session to extract store information
        const { storeHash } = await getSession(req);

        const { feeRuleId } = req.body;

        // Validate required fields
        if (!feeRuleId) {
            res.status(400).json({
                error: 'Missing required field: feeRuleId is required'
            });
            return;
        }

        // Check if fee rule exists
        const existingFeeRule = await db.getFeeRule(storeHash, feeRuleId);
        if (!existingFeeRule) {
            res.status(404).json({
                error: 'Fee rule not found'
            });
            return;
        }

        // Attempt fee synchronization with BigCommerce before deletion
        let synchronizationResult = null;
        try {
            console.log(`Attempting fee synchronization for deleted rule "${existingFeeRule.name}"`);
            synchronizationResult = await synchronizeFeeDeletion(storeHash, existingFeeRule);

            if (synchronizationResult.success) {
                console.log(`Fee synchronization successful for deleted rule "${existingFeeRule.name}"`);
            } else {
                console.warn(`Fee synchronization failed for deleted rule "${existingFeeRule.name}":`, synchronizationResult.error);
            }
        } catch (error) {
            console.error(`Error during fee synchronization for deleted rule "${existingFeeRule.name}":`, error);
            synchronizationResult = {
                success: false,
                action: 'delete',
                error: error.message || 'Unknown synchronization error'
            };
        }

        // Delete the fee rule from the database
        await db.deleteFeeRule(storeHash, feeRuleId);

        res.status(200).json({
            success: true,
            message: 'Fee rule deleted successfully',
            data: { feeRuleId },
            synchronization: synchronizationResult ? {
                attempted: true,
                success: synchronizationResult.success,
                action: synchronizationResult.action,
                checkoutId: synchronizationResult.checkoutId,
                bigCommerceFeeId: synchronizationResult.bigCommerceFeeId,
                error: synchronizationResult.error,
                details: synchronizationResult.details
            } : {
                attempted: false,
                reason: 'Synchronization not attempted'
            }
        });

    } catch (error) {
        console.error('Error deleting fee rule:', error);
        res.status(500).json({
            error: 'Internal server error',
            message: error.message
        });
    }
}
